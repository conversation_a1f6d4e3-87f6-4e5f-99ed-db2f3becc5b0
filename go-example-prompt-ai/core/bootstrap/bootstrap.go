package bootstrap

import (
	"context"
	"fmt"
	"log/slog"
	"sync"

	"github.com/samber/do"
)

type TeardownFunc func(ctx context.Context) error

type Container interface {
	Injector() *do.Injector
}

type Provider interface {
	Register(ctx context.Context, container Container) error
	Boot(ctx context.Context, container Container) error
	Teardown(ctx context.Context, container Container) error
}

type BootStrapper interface {
	Container() Container
	Register(ctx context.Context) error
	Boot(ctx context.Context) error
	Shutdown(ctx context.Context) error
	Providers() []Provider
}

var _ Container = (*DefaulContainer)(nil)

type DefaulContainer struct {
	injector *do.Injector
}

func NewContainer(i *do.Injector) *DefaulContainer {
	return &DefaulContainer{
		injector: i,
	}
}

func (c *DefaulContainer) Injector() *do.Injector {
	return c.injector
}

func Invoke[T any](ctx context.Context, container Container) (T, error) {
	return do.Invoke[T](container.Injector())
}

func MustInvoke[T any](ctx context.Context, container Container) T {
	return do.MustInvoke[T](container.Injector())
}

func Provide[T any](ctx context.Context, container Container, provider func(*do.Injector) (T, error)) error {
	return do.Provide(container.Injector(), provider)
}

var _ Provider = (*DefaultProvider)(nil)

type DefaultProvider struct{}

func (s *DefaultProvider) Register(ctx context.Context, container Container) error {
	return nil
}

func (s *DefaultProvider) Boot(ctx context.Context, container Container) error {
	return nil
}

func (s *DefaultProvider) Teardown(ctx context.Context, container Container) error {
	return nil
}

var _ BootStrapper = (*DefaultBootStrapper)(nil)

type DefaultBootStrapper struct {
	container    *DefaulContainer
	providers    []Provider
	booted       bool
	serviceMutex sync.Mutex
	registered   bool
	shutdown     bool
}

func NewDefaultBootStrapper(i *do.Injector, providers ...Provider) *DefaultBootStrapper {
	return &DefaultBootStrapper{
		container:    NewContainer(i),
		providers:    providers,
		booted:       false,
		registered:   false,
		serviceMutex: sync.Mutex{},
	}
}

func (b *DefaultBootStrapper) Container() Container {
	return b.container
}

func (b *DefaultBootStrapper) Register(ctx context.Context) error {
	if b.registered {
		slog.Warn("bootstrapper is already registered")
		return nil
	}
	b.registered = true
	for _, service := range b.providers {
		if err := service.Register(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Boot(ctx context.Context) error {
	if b.booted {
		slog.Warn("bootstrapper is already booted")
		return nil
	}
	b.booted = true
	for _, service := range b.providers {
		if err := service.Boot(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Shutdown(ctx context.Context) error {
	var errs []error
	if b.shutdown {
		slog.Warn("bootstrapper already shut down")
		return nil
	}
	b.shutdown = true

	// Iterate through providers in reverse order
	for i := len(b.providers) - 1; i >= 0; i-- {
		if err := b.providers[i].Teardown(ctx, b.container); err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		err := fmt.Errorf("multiple teardown errors: %v", errs)
		slog.Error("shutdown failed", slog.Any("errors", errs))
		return err
	}
	return nil
}

func (b *DefaultBootStrapper) Providers() []Provider {
	b.serviceMutex.Lock()
	defer b.serviceMutex.Unlock()
	return b.providers
}
